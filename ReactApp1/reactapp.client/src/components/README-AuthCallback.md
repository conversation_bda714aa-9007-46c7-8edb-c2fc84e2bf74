# Azure AD Authentication Callback Fallback

## Overview

This implementation provides a fallback route handler for the Azure AD authentication callback URL `https://localhost:5173/api/auth/callback/azure-ad`.

## Components

### AuthCallback.tsx
- **Purpose**: Handles Azure AD authentication redirects
- **Route**: `/api/auth/callback/azure-ad`
- **Functionality**:
  - Processes authentication responses from Azure AD
  - Handles both successful and failed authentication attempts
  - Provides user feedback during the authentication process
  - Automatically redirects users to appropriate pages

### Features

1. **Comprehensive Error Handling**
   - Catches and displays authentication errors
   - Parses URL parameters for error information
   - Provides meaningful error messages to users

2. **Multiple Authentication Flows**
   - Supports both popup and redirect authentication
   - Handles existing authenticated sessions
   - Manages MSAL interaction states

3. **User Experience**
   - Loading indicators during processing
   - Success/error visual feedback
   - Automatic redirects with appropriate delays
   - Responsive design for mobile devices

4. **Logging and Debugging**
   - Comprehensive console logging
   - Authentication state tracking
   - Error reporting for troubleshooting

## Usage

### Redirect Authentication Flow
1. User clicks "Sign in with Microsoft (Redirect)" on login page
2. User is redirected to Azure AD for authentication
3. Azure AD redirects back to `/api/auth/callback/azure-ad`
4. AuthCallback component processes the response
5. User is redirected to the main application or login page

### Popup Authentication Flow
1. User clicks "Sign in with Microsoft (Popup)" on login page
2. Authentication happens in a popup window
3. No callback route is needed for popup flow
4. User remains on the current page

## Configuration

The callback route is configured in:
- **App.tsx**: Route definition
- **authConfig.ts**: MSAL redirect URI configuration
- **AuthCallback.css**: Styling for the callback page

## Error Scenarios Handled

1. **Authentication Failure**: User cancels or authentication fails
2. **Network Issues**: Connection problems during authentication
3. **Invalid Tokens**: Malformed or expired authentication responses
4. **Missing Accounts**: No authenticated accounts found
5. **MSAL Errors**: Library-specific authentication errors

## Testing

To test the callback functionality:
1. Use redirect authentication flow
2. Monitor browser network tab for redirect calls
3. Check console logs for authentication state changes
4. Verify proper redirects after authentication

## Security Considerations

- All authentication is handled by MSAL library
- No sensitive data is stored in component state
- Automatic cleanup of authentication state on errors
- Secure redirect handling to prevent open redirects
