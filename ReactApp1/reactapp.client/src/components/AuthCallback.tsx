import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMsal } from '@azure/msal-react';
import { InteractionStatus } from '@azure/msal-browser';
import './AuthCallback.css';

interface AuthCallbackState {
    loading: boolean;
    error: string | null;
    success: boolean;
}

const AuthCallback: React.FC = () => {
    const { instance, accounts, inProgress } = useMsal();
    const navigate = useNavigate();
    const [state, setState] = useState<AuthCallbackState>({
        loading: true,
        error: null,
        success: false
    });

    useEffect(() => {
        const handleCallback = async () => {
            try {
                console.log('AuthCallback: Starting authentication callback handling');
                console.log('AuthCallback: Current accounts:', accounts.length);
                console.log('AuthCallback: Interaction status:', inProgress);

                // Handle the redirect response from Azure AD
                const response = await instance.handleRedirectPromise();
                console.log('AuthCallback: Redirect response:', response);

                if (response) {
                    // Authentication was successful
                    console.log('Authentication successful:', response);
                    setState({
                        loading: false,
                        error: null,
                        success: true
                    });

                    // Set the active account
                    if (response.account) {
                        instance.setActiveAccount(response.account);
                        console.log('AuthCallback: Active account set:', response.account.username);
                    }

                    // Redirect to the main application
                    setTimeout(() => {
                        navigate('/weather', { replace: true });
                    }, 2000);

                } else if (accounts.length > 0) {
                    // User is already authenticated
                    console.log('User already authenticated');
                    setState({
                        loading: false,
                        error: null,
                        success: true
                    });

                    setTimeout(() => {
                        navigate('/weather', { replace: true });
                    }, 1000);

                } else {
                    // Check URL parameters for error information
                    const urlParams = new URLSearchParams(window.location.search);
                    const error = urlParams.get('error');
                    const errorDescription = urlParams.get('error_description');

                    if (error) {
                        console.error('Authentication error from URL:', error, errorDescription);
                        setState({
                            loading: false,
                            error: `Authentication failed: ${error}. ${errorDescription || ''}`,
                            success: false
                        });
                    } else {
                        // No authentication response and no existing accounts
                        console.log('No authentication response received');
                        setState({
                            loading: false,
                            error: 'No authentication response received. Please try logging in again.',
                            success: false
                        });
                    }

                    setTimeout(() => {
                        navigate('/login', { replace: true });
                    }, 3000);
                }

            } catch (error) {
                console.error('Authentication callback error:', error);
                setState({
                    loading: false,
                    error: error instanceof Error ? error.message : 'Authentication failed',
                    success: false
                });

                // Redirect to login page after showing error
                setTimeout(() => {
                    navigate('/login', { replace: true });
                }, 5000);
            }
        };

        // Only handle callback if MSAL is not currently processing an interaction
        if (inProgress === InteractionStatus.None) {
            handleCallback();
        }
    }, [instance, accounts, navigate, inProgress]);

    // Show loading state while MSAL is processing
    if (inProgress !== InteractionStatus.None || state.loading) {
        return (
            <div className="auth-callback-container">
                <div className="auth-callback-content">
                    <div className="spinner"></div>
                    <h2>Processing authentication...</h2>
                    <p>Please wait while we complete your sign-in.</p>
                </div>
            </div>
        );
    }

    // Show success state
    if (state.success) {
        return (
            <div className="auth-callback-container">
                <div className="auth-callback-content">
                    <div className="success-icon">✓</div>
                    <h2>Authentication successful!</h2>
                    <p>Redirecting you to the application...</p>
                </div>
            </div>
        );
    }

    // Show error state
    if (state.error) {
        return (
            <div className="auth-callback-container">
                <div className="auth-callback-content">
                    <div className="error-icon">✗</div>
                    <h2>Authentication failed</h2>
                    <p>{state.error}</p>
                    <p>You will be redirected to the login page shortly.</p>
                </div>
            </div>
        );
    }

    return null;
};

export default AuthCallback;
