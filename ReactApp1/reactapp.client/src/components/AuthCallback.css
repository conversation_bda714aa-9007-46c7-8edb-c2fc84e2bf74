.auth-callback-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f5f5f5;
    padding: 20px;
}

.auth-callback-content {
    background: white;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 100%;
}

.auth-callback-content h2 {
    margin: 20px 0 10px 0;
    color: #333;
    font-size: 24px;
}

.auth-callback-content p {
    color: #666;
    margin: 10px 0;
    line-height: 1.5;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.success-icon {
    font-size: 48px;
    color: #28a745;
    margin-bottom: 20px;
}

.error-icon {
    font-size: 48px;
    color: #dc3545;
    margin-bottom: 20px;
}

/* Responsive design */
@media (max-width: 480px) {
    .auth-callback-content {
        padding: 30px 20px;
    }
    
    .auth-callback-content h2 {
        font-size: 20px;
    }
    
    .success-icon,
    .error-icon {
        font-size: 36px;
    }
}
