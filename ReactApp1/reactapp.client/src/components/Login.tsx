import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMsal } from '@azure/msal-react';
import { loginRequest } from '../config/authConfig';

const Login: React.FC = () => {
    const { instance, accounts } = useMsal();
    const navigate = useNavigate();
    const isAuthenticated = accounts.length > 0;

    useEffect(() => {
        if (isAuthenticated) {
            navigate('/weather', { replace: true });
        }
    }, [isAuthenticated, navigate]);

    const handleLogin = async () => {
        try {
            // Use redirect instead of popup for better compatibility
            await instance.loginRedirect(loginRequest);
        } catch (error) {
            console.error('Login failed:', error);
        }
    };

    const handlePopupLogin = async () => {
        try {
            await instance.loginPopup(loginRequest);
        } catch (error) {
            console.error('Popup login failed:', error);
        }
    };

    return (
        <div className="login-page">
            <div className="login-card">
                <button onClick={handleLogin} className="btn btn-primary btn-large">
                    Sign in with Microsoft
                </button>
            </div>
        </div>
    );
};

export default Login;