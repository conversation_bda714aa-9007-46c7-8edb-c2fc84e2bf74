import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import WeatherDashboard from './components/WeatherDashboard';
import ProtectedRoute from './components/ProtectedRoute';
import Login from './components/Login';
import AuthCallback from './components/AuthCallback';
import './App.css';
import Layout from "./components/Layout.tsx";
import UserProfile from "./components/UserProfile.tsx";

function App() {
    return (
        <Router>
            <Routes>
                <Route path="/" element={<Layout />}>
                    <Route index element={<Navigate to="/weather" replace />} />
                    <Route path="/login" element={<Login />} />
                    <Route path="/api/auth/callback/azure-ad" element={<AuthCallback />} />
                    <Route
                        path="/weather"
                        element={
                            <ProtectedRoute>
                                <WeatherDashboard />
                            </ProtectedRoute>
                        }
                    />
                    <Route
                        path="/profile"
                        element={
                            <ProtectedRoute>
                                <UserProfile />
                            </ProtectedRoute>
                        }
                    />
                </Route>
            </Routes>
        </Router>
    );
}

export default App;